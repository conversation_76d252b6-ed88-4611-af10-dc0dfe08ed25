// ───────────────────────────────────────
// PlayerController.cs  • clean version
// ───────────────────────────────────────
using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class PlayerController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    /* ── Movement ─────────────────────────────────────────────── */
    [Header("Movement settings")]
    public float walkSpeed = 8f;
    public float runSpeed = 14f;
    public float jumpForce = 20f;
    public float vSmoothTime = 0.10f;
    public float airSmoothTime = 0.50f;
    public float stickToGroundForce = 8f;
    public float groundCheckDistance = 0.10f;
    public float slopeLimit = 45f;
    public float slideSpeed = 5f;
    public float airControl = 0.10f;

    /* ── Jet-pack ─────────────────────────────────────────────── */
    [Head<PERSON>("Jet-pack settings")]
    public float jetpackForce = 10f;
    public float jetpackDuration = 2f;
    public float jetpackRefuelTime = 2f;
    public float jetpackRefuelDelay = 2f;

    /* ── Mouse ────────────────────────────────────────────────── */
    [Header("Mouse settings")]
    public float mouseSensitivityMultiplier = 1f;
    public float maxMouseSmoothTime = 0.3f;
    public Vector2 pitchMinMax = new Vector2(-40, 85);
    public InputSettings inputSettings;

    /* ── References ───────────────────────────────────────────── */
    [Header("References")]
    public Transform cameraTransform;             // main camera
    public Transform groundCheck;                 // sphere cast origin
    public Transform modelTransform;              // purely visual (optional)
    public LayerMask groundMask;

    /* ── Private state ────────────────────────────────────────── */
    Rigidbody rb;
    GravityAffector gravityAffector;
    Ship currentShip;
    Vector3 lastShipVelocity;  // Track ship velocity for inheritance

    bool isGrounded;
    bool usingJetpack;
    float jetpackFuelPercent = 1f;
    float lastJetpackUseTime;

    bool cursorLocked = true;

    // camera rotation
    float yaw, pitch, smoothYaw, smoothPitch;
    float yawSmoothV, pitchSmoothV;
    Vector3 smoothVelRef;



    public Vector3 CameraInitialLocalPos { get; private set; }

    /* ─────────────────────────────────────────────────────────── */
    #region Unity-lifecycle
    void Awake()
    {
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        rb.interpolation = RigidbodyInterpolation.Interpolate;
        rb.mass = 70f;

        gravityAffector = GetComponent<GravityAffector>() ??
                          gameObject.AddComponent<GravityAffector>();

        // fallback assignments
        cameraTransform ??= GetComponentInChildren<Camera>()?.transform;
        groundCheck ??= transform.Find("Feet");
        modelTransform ??= transform.Find("Model");

        if (cameraTransform == null)
        {
            Debug.LogError("PlayerController: no camera found.");
            enabled = false;
            return;
        }

        CameraInitialLocalPos = cameraTransform.localPosition;

        // Ensure input settings are properly initialized
        if (inputSettings == null)
        {
            inputSettings = ScriptableObject.CreateInstance<InputSettings>();
            inputSettings.mouseSensitivity = 100f;  // Default sensitivity
            inputSettings.mouseSmoothing = 0.2f;    // Default smoothing
        }
        inputSettings.Begin();

        yaw = transform.eulerAngles.y;
        pitch = cameraTransform.localEulerAngles.x;
        smoothYaw = yaw;
        smoothPitch = pitch;

        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    void Update()
    {
        ReadInput();
        RotateCamera();
        UpdateJetpackFuel();
    }

    void FixedUpdate()
    {
        Vector3 gDir = currentShip
                     ? currentShip.GetArtificialGravityDirection()
                     : gravityAffector.GetGravityDirection();

        // DEBUG: Log gravity source
        if (Input.GetKey(KeyCode.L))
        {
            Debug.Log($"[FixedUpdate] Using ship gravity: {currentShip != null}");
            Debug.Log($"[FixedUpdate] Gravity direction: {gDir}");
            if (currentShip != null)
            {
                Debug.Log($"[FixedUpdate] Ship transform.up: {currentShip.transform.up}");
            }
        }

        // Handle ship velocity inheritance BEFORE other physics
        // Only use built-in velocity inheritance if no ShipMovementSync is handling it
        if (currentShip != null && !IsShipMovementSyncActive())
        {
            InheritShipVelocity();
        }
        else if (currentShip != null && Input.GetKey(KeyCode.K))
        {
            Debug.Log("[PlayerController] ShipMovementSync is handling velocity inheritance");
        }

        AlignToGravity(gDir);
        CheckGround(gDir);
        MoveCharacter(gDir);
    }
    #endregion

    /* ── Input / camera ───────────────────────────────────────── */

    void ReadInput()
    {
        if (Input.GetKeyDown(KeyCode.Space) && isGrounded)
            Jump();

        usingJetpack = Input.GetKey(KeyCode.Space) &&
                       !isGrounded &&
                       jetpackFuelPercent > 0f;

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            cursorLocked = !cursorLocked;
            Cursor.lockState = cursorLocked ? CursorLockMode.Locked
                                            : CursorLockMode.None;
            Cursor.visible = !cursorLocked;
        }
    }

    void RotateCamera()
    {
        if (!cursorLocked)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            cursorLocked = true;
            return;
        }

        // TRY DIFFERENT MOUSE INPUT METHODS
        float mx = Input.GetAxis("Mouse X");  // Try GetAxis instead of GetAxisRaw
        float my = Input.GetAxis("Mouse Y");

        // ALSO TRY: Manual mouse input for testing
        if (Input.GetKey(KeyCode.LeftArrow))
            mx = -1f;
        if (Input.GetKey(KeyCode.RightArrow))
            mx = 1f;
        if (Input.GetKey(KeyCode.UpArrow))
            my = 1f;
        if (Input.GetKey(KeyCode.DownArrow))
            my = -1f;

        // Log only when there's actual input
        if (mx != 0 || my != 0)
        {
            Debug.Log($"[RotateCamera] Mouse input detected - X: {mx:F3}, Y: {my:F3}");
        }

        float sens = inputSettings.mouseSensitivity * mouseSensitivityMultiplier;

        yaw += mx * sens * Time.deltaTime;
        pitch -= my * sens * Time.deltaTime;
        pitch = Mathf.Clamp(pitch, pitchMinMax.x, pitchMinMax.y);

        float smTime = Mathf.Lerp(0.01f, maxMouseSmoothTime,
                                    inputSettings.mouseSmoothing);
        smoothPitch = Mathf.SmoothDampAngle(smoothPitch, pitch,
                                               ref pitchSmoothV, smTime);
        float oldYaw = smoothYaw;
        smoothYaw = Mathf.SmoothDampAngle(smoothYaw, yaw,
                                               ref yawSmoothV, smTime);

        // Check if player is parented to ship
        bool isParentedToShip = transform.parent != null &&
                               transform.parent.GetComponent<Ship>() != null;

        // Always apply pitch to camera (works the same in both cases)
        cameraTransform.localEulerAngles = Vector3.right * smoothPitch;

        // CRITICAL FIX: Apply rotation to RIGIDBODY instead of Transform
        // This prevents the physics system from overriding our rotation
        if (isParentedToShip)
        {
            Transform shipTransform = transform.parent;
            Vector3 shipUp = shipTransform.up;
            float yawDelta = Mathf.DeltaAngle(oldYaw, smoothYaw);

            // Apply to rigidbody rotation
            rb.rotation = rb.rotation * Quaternion.AngleAxis(yawDelta, shipUp);

            if (yawDelta != 0)
                Debug.Log($"[RotateCamera] PARENTED rb rotation - Yaw Delta: {yawDelta:F4}");
        }
        else
        {
            float yawDelta = Mathf.DeltaAngle(oldYaw, smoothYaw);

            // Apply to rigidbody rotation
            rb.rotation = rb.rotation * Quaternion.AngleAxis(yawDelta, Vector3.up);

            if (yawDelta != 0)
                Debug.Log($"[RotateCamera] NORMAL rb rotation - Yaw Delta: {yawDelta:F4}");
        }
    }

    /* ── Character movement ───────────────────────────────────── */

    void MoveCharacter(Vector3 gDir)
    {
        float h = Input.GetAxisRaw("Horizontal");
        float v = Input.GetAxisRaw("Vertical");
        bool run = Input.GetKey(KeyCode.LeftShift);

        Vector3 input = new(h, 0f, v);
        if (input.sqrMagnitude > 1f) input.Normalize();

        float speed = run ? runSpeed : walkSpeed;
        Vector3 targetVel = transform.TransformDirection(input) * speed;
        targetVel = Vector3.ProjectOnPlane(targetVel, gDir);

        // Check if player is parented to ship (handled by ShipMovementSync)
        bool isParentedToShip = transform.parent != null &&
                               transform.parent.GetComponent<Ship>() != null &&
                               IsShipMovementSyncActive();

        if (isParentedToShip)
        {
            // Player is parented to ship - let ShipMovementSync handle base velocity
            // Only apply relative movement on top of ship velocity
            Vector3 currentVelocity = rb.linearVelocity;
            Vector3 lateral = Vector3.ProjectOnPlane(currentVelocity, gDir);
            Vector3 vertical = Vector3.Project(currentVelocity, gDir);

            // Apply player input as relative movement (much smaller influence)
            Vector3 relativeMovement = targetVel * 0.3f;  // Reduced influence when parented
            lateral += relativeMovement;

            rb.linearVelocity = lateral + vertical;

            if (Input.GetKey(KeyCode.K) && input.sqrMagnitude > 0.1f)
            {
                Debug.Log($"[MoveCharacter] Parented movement - Input: {input}, Relative: {relativeMovement}");
            }
        }
        else
        {
            // Normal movement when not parented to ship
            Vector3 lateral = Vector3.ProjectOnPlane(rb.linearVelocity, gDir);
            Vector3 vertical = Vector3.Project(rb.linearVelocity, gDir);

            float smooth = isGrounded ? vSmoothTime : airSmoothTime;
            lateral = Vector3.SmoothDamp(lateral, targetVel,
                                                    ref smoothVelRef, smooth);

            rb.linearVelocity = lateral + vertical;
        }

        if (usingJetpack)
            rb.AddForce(-gDir * jetpackForce, ForceMode.Acceleration);

        if (isGrounded)
            rb.AddForce(-gDir * stickToGroundForce, ForceMode.Acceleration);
    }

    void CheckGround(Vector3 gDir)
    {
        if (groundCheck == null) { isGrounded = false; return; }

        // Use a slightly larger check distance for better detection
        float checkDist = groundCheckDistance * 1.2f;
        isGrounded = Physics.CheckSphere(groundCheck.position, checkDist, groundMask);

        // Additional raycast check for more reliable ground detection
        if (!isGrounded)
        {
            isGrounded = Physics.Raycast(groundCheck.position, gDir, checkDist * 2f, groundMask);
        }

        if (!isGrounded) return;

        // slope handling
        if (Physics.Raycast(groundCheck.position, gDir,
                            out var hit, checkDist * 2f, groundMask))
        {
            float angle = Vector3.Angle(hit.normal, -gDir);
            if (angle > slopeLimit)
            {
                Vector3 slideDir =
                    Vector3.ProjectOnPlane(gDir, hit.normal).normalized;
                rb.linearVelocity += slideDir * slideSpeed * Time.deltaTime;
            }
        }
    }

    void Jump()
    {
        Vector3 gDir = currentShip
                     ? currentShip.GetArtificialGravityDirection()
                     : gravityAffector.GetGravityDirection();

        rb.AddForce(-gDir * jumpForce, ForceMode.Impulse);
    }

    /* ── Jet-pack fuel ───────────────────────────────────────── */

    void UpdateJetpackFuel()
    {
        if (usingJetpack)
        {
            jetpackFuelPercent =
                Mathf.Max(0f, jetpackFuelPercent -
                               (1f / jetpackDuration) * Time.deltaTime);
            lastJetpackUseTime = Time.time;
        }
        else if (Time.time - lastJetpackUseTime > jetpackRefuelDelay)
        {
            jetpackFuelPercent =
                Mathf.Min(1f, jetpackFuelPercent +
                               (1f / jetpackRefuelTime) * Time.deltaTime);
        }
    }

    /* ── Gravity alignment ───────────────────────────────────── */
    void AlignToGravity(Vector3 gDir)
    {
        // DISABLE GRAVITY ALIGNMENT TEMPORARILY TO TEST MOUSE ROTATION
        // The issue is that something is resetting transform.rotation to (0,0,0)
        // Let's disable this to see if mouse rotation works without interference

        Debug.Log($"[AlignToGravity] DISABLED - gDir: {gDir}");
        Debug.Log($"[AlignToGravity] Current rb.rotation: {rb.rotation.eulerAngles}");
        Debug.Log($"[AlignToGravity] Current transform.rotation: {transform.eulerAngles}");

        // TEMPORARILY DISABLED - NO GRAVITY ALIGNMENT
        // Vector3 gravityUp = -gDir.normalized;
        // Quaternion gravityAlignment = Quaternion.FromToRotation(transform.up, gravityUp);
        // rb.rotation = gravityAlignment * rb.rotation;

        Debug.Log($"[AlignToGravity] After (no changes) - rb.rotation: {rb.rotation.eulerAngles}");
        Debug.Log($"[AlignToGravity] After (no changes) - transform.rotation: {transform.eulerAngles}");

        // optional: keep suit model perfectly upright relative to body
        if (modelTransform != null)
            modelTransform.localRotation = Quaternion.identity;
    }

    /* ── Ship velocity inheritance ──────────────────────────── */

    /// <summary>
    /// Check if a ShipMovementSync component is active and handling velocity inheritance
    /// </summary>
    bool IsShipMovementSyncActive()
    {
        ShipMovementSync shipSync = FindFirstObjectByType<ShipMovementSync>();
        if (shipSync == null) return false;

        // Check if the ShipMovementSync is enabled and configured to handle velocity inheritance
        return shipSync.enabled && shipSync.gameObject.activeInHierarchy;
    }

    void InheritShipVelocity()
    {
        if (currentShip == null) return;

        Rigidbody shipRb = currentShip.GetComponent<Rigidbody>();
        if (shipRb == null) return;

        // Get current ship velocity
        Vector3 currentShipVelocity = shipRb.linearVelocity;

        // Calculate ship velocity change since last frame
        Vector3 shipVelocityDelta = currentShipVelocity - lastShipVelocity;

        // Apply ship velocity change to player while preserving relative movement
        // This ensures the player moves with the ship without losing their own movement
        rb.linearVelocity += shipVelocityDelta;

        // Debug logging for velocity inheritance (press K to enable)
        if (Input.GetKey(KeyCode.K))
        {
            Debug.Log($"[InheritShipVelocity] Ship velocity: {currentShipVelocity}");
            Debug.Log($"[InheritShipVelocity] Ship velocity delta: {shipVelocityDelta}");
            Debug.Log($"[InheritShipVelocity] Player velocity before: {rb.linearVelocity - shipVelocityDelta}");
            Debug.Log($"[InheritShipVelocity] Player velocity after: {rb.linearVelocity}");
            Debug.Log($"[InheritShipVelocity] Player relative velocity: {rb.linearVelocity - currentShipVelocity}");
        }

        // Store current ship velocity for next frame
        lastShipVelocity = currentShipVelocity;
    }

    /* ── Ship hooks ──────────────────────────────────────────── */
    public void EnterShipInterior(Ship ship)
    {
        // Prevent duplicate calls
        if (currentShip == ship) return;

        currentShip = ship;
        gravityAffector.SwitchToInteriorGravity(
            ship.GetComponent<ShipGravity>());

        // Reset camera rotation values when entering ship to prevent conflicts
        // This ensures smooth camera control when parented to ship
        ResetCameraRotationForShip();

        // Only handle velocity inheritance if ShipMovementSync is not active
        if (!IsShipMovementSyncActive())
        {
            // Initialize ship velocity tracking
            Rigidbody shipRb = ship.GetComponent<Rigidbody>();
            if (shipRb != null)
            {
                // Store current player velocity to preserve relative movement
                Vector3 playerWorldVelocity = rb.linearVelocity;

                // Set initial ship velocity tracking
                lastShipVelocity = shipRb.linearVelocity;

                // Synchronize player velocity with ship while preserving any existing movement
                // This prevents sudden velocity jumps when entering the ship
                Vector3 playerRelativeVelocity = playerWorldVelocity - shipRb.linearVelocity;
                rb.linearVelocity = shipRb.linearVelocity + Vector3.ClampMagnitude(playerRelativeVelocity, 10f);

                Debug.Log($"[EnterShipInterior] Ship velocity: {shipRb.linearVelocity}");
                Debug.Log($"[EnterShipInterior] Player velocity set to: {rb.linearVelocity}");
            }
        }
        else
        {
            Debug.Log($"[EnterShipInterior] ShipMovementSync is handling velocity inheritance");
        }
    }

    void ResetCameraRotationForShip()
    {
        // Reset camera rotation values to current state to prevent jumps
        // when switching between parented and non-parented camera control
        bool isParentedToShip = transform.parent != null &&
                               transform.parent.GetComponent<Ship>() != null;

        if (isParentedToShip)
        {
            // When parented, initialize yaw/pitch from current camera rotation
            Vector3 currentCameraRotation = cameraTransform.localEulerAngles;
            yaw = currentCameraRotation.y;
            pitch = currentCameraRotation.x;

            // Handle angle wrapping for pitch
            if (pitch > 180f) pitch -= 360f;

            smoothYaw = yaw;
            smoothPitch = pitch;

            Debug.Log($"[ResetCameraRotationForShip] Reset camera rotation - Yaw: {yaw:F1}, Pitch: {pitch:F1}");
        }
        else
        {
            // When not parented, initialize from transform rotation
            yaw = transform.eulerAngles.y;
            pitch = cameraTransform.localEulerAngles.x;
            if (pitch > 180f) pitch -= 360f;

            smoothYaw = yaw;
            smoothPitch = pitch;
        }
    }

    public void ExitFromSpaceship()
    {
        gravityAffector.SwitchToPlanetaryGravity();
        currentShip = null;
        lastShipVelocity = Vector3.zero;  // Reset ship velocity tracking

        // Reset camera rotation when exiting ship
        ResetCameraRotationForShip();
    }

    /* ── Public conveniences ─────────────────────────────────── */
    public Camera Camera => cameraTransform.GetComponent<Camera>();
    public Rigidbody Rigidbody => rb;
}
